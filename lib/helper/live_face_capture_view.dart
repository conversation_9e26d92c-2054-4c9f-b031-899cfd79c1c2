import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:camera/camera.dart';
import 'package:face_recognition/helper/face_painter.dart';
import 'package:face_recognition/models/face_registry.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart'; // This line is already present, no change needed
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

class LiveFaceCaptureView extends StatefulWidget {
  const LiveFaceCaptureView({super.key});

  @override
  State<LiveFaceCaptureView> createState() => _LiveFaceCaptureViewState();
}

class _LiveFaceCaptureViewState extends State<LiveFaceCaptureView> {
  CameraController? _cameraController;
  final _faceDetector = FaceDetector(
    options: FaceDetectorOptions(
      performanceMode: FaceDetectorMode.fast,
      enableTracking: true,
    ),
  );
  bool _isDetecting = false;
  XFile? _capturedImageFile;
  List<FaceMatch> _detectedFaces = [];
  ui.Image? _displayImage;
  Size? _imageSize;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    final cameras = await availableCameras();
    // Use the front camera
    final cameraDescription = cameras.firstWhere(
      (camera) => camera.lensDirection == CameraLensDirection.front,
      orElse: () => cameras.first,
    );

    _cameraController = CameraController(
      cameraDescription,
      ResolutionPreset.high,
      enableAudio: false,
      imageFormatGroup: Platform.isAndroid
          ? ImageFormatGroup
                .nv21 // for Android
          : ImageFormatGroup.bgra8888, // for iOS
    );

    await _cameraController!.initialize();
    if (!mounted) return;

    // Start streaming images for face detection
    _cameraController!.startImageStream(_processCameraImage);

    setState(() {});
  }

  void _processCameraImage(CameraImage image) async {
    if (_isDetecting || _capturedImageFile != null) return;

    _isDetecting = true;

    final inputImage = _inputImageFromCameraImage(image);
    if (inputImage == null) {
      _isDetecting = false;
      return;
    }

    final faces = await _faceDetector.processImage(inputImage);

    if (faces.isNotEmpty && _capturedImageFile == null) {
      // Face found! Stop the stream and capture the image.
      await _cameraController?.stopImageStream();
      final XFile picture = await _cameraController!.takePicture();

      // This is a placeholder for your actual face recognition logic.
      // You would take the detected `Face` from ML Kit and the captured image,
      // and then run your matching algorithm to produce `FaceMatch` objects.
      final faceMatches = await _getFaceMatches(picture, faces);
      final ui.Image displayImage = await _loadImage(picture);

      setState(() {
        _capturedImageFile = picture;
        _detectedFaces = faceMatches;
        _displayImage = displayImage;
        // Store the image size for the painter. The image from the stream and the
        // final captured image might have different dimensions on some devices.
        // We use the final image's dimensions.
        _imageSize = Size(
          displayImage.width.toDouble(),
          displayImage.height.toDouble(),
        );
      });
    }

    _isDetecting = false;
  }

  // This is a placeholder for your face recognition logic.
  Future<List<FaceMatch>> _getFaceMatches(
    XFile image,
    List<Face> detectedFaces,
  ) async {
    // This is where you'd implement your logic to compare the detected face
    // with your registered faces to get a `FaceMatch`.
    // For demonstration, we'll just create a placeholder.
    List<FaceMatch> matches = [];
    for (var face in detectedFaces) {
      matches.add(
        FaceMatch(
          boundingRect: face.boundingBox,
          // Replace this with your actual user matching logic
          user: RegisteredUser(id: "unknown", name: "Person"),
          // And your actual difference/accuracy score
          difference: 0.85, // Example value
        ),
      );
    }
    return matches;
  }

  Future<ui.Image> _loadImage(XFile file) async {
    final data = await file.readAsBytes();
    return await decodeImageFromList(data);
  }

  InputImage? _inputImageFromCameraImage(CameraImage image) {
    final camera = _cameraController!.description;
    final sensorOrientation = camera.sensorOrientation;
    final rotation = InputImageRotationValue.fromRawValue(sensorOrientation);

    if (rotation == null) return null;

    final format = InputImageFormatValue.fromRawValue(image.format.raw);
    if (format == null) return null;

    if (image.planes.length != 1) return null;
    final plane = image.planes.first;

    return InputImage.fromBytes(
      bytes: plane.bytes,
      metadata: InputImageMetadata(
        size: Size(image.width.toDouble(), image.height.toDouble()),
        rotation: rotation,
        format: format,
        bytesPerRow: plane.bytesPerRow,
      ),
    );
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    _faceDetector.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (_capturedImageFile != null && _displayImage != null) {
      // A face was detected and a picture was taken.
      // Display the picture with the bounding boxes.
      return Scaffold(
        appBar: AppBar(title: const Text("Face Captured")),
        body: Center(
          child: FittedBox(
            child: SizedBox(
              width: _imageSize!.width,
              height: _imageSize!.height,
              child: CustomPaint(
                painter: FaceDetectorPainter(
                  _detectedFaces,
                  _imageSize!,
                  _displayImage!,
                ),
              ),
            ),
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () {
            // Reset to go back to live detection
            setState(() {
              _capturedImageFile = null;
              _displayImage = null;
              _detectedFaces = [];
            });
            _cameraController!.startImageStream(_processCameraImage);
          },
          child: const Icon(Icons.refresh),
        ),
      );
    }

    // Show the live camera preview
    return Scaffold(
      appBar: AppBar(title: const Text("Look at the Camera")),
      body: CameraPreview(_cameraController!),
    );
  }
}
